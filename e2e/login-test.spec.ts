import { expect } from "@playwright/test";
import { test } from "./fixture";

test.beforeEach(async ({ page }) => {
  // 导航到登录页面（请替换为实际的登录页面URL）
  await page.goto("https://your-login-page.com");
  await page.waitForLoadState("networkidle");
});

test("登录功能测试", async ({
  ai,
  aiQuery,
  aiAssert,
  aiInput,
  aiTap,
  aiWaitFor
}) => {
  // 步骤1: 用户名输入框输入bijibo
  await aiInput('用户名输入框', 'bijibo');
  
  // 步骤2: 密码输入框输入123456
  await aiInput('密码输入框', '123456');
  
  // 步骤3: 点击登录
  await aiTap('登录按钮');
  
  // 可选：等待登录完成并验证结果
  await aiWaitFor('登录成功，页面跳转到主页', { timeoutMs: 10000 });
  
  // 可选：验证登录成功
  await aiAssert('用户已成功登录，显示用户信息或欢迎信息');
});

// 更详细的版本，包含更多验证和错误处理
test("登录功能测试 - 详细版本", async ({
  ai,
  aiQuery,
  aiAssert,
  aiInput,
  aiTap,
  aiWaitFor
}) => {
  // 步骤1: 用户名输入框输入bijibo
  await aiInput('用户名或邮箱输入框', 'bijibo');
  
  // 验证用户名已输入
  await aiAssert('用户名输入框中已输入bijibo');
  
  // 步骤2: 密码输入框输入123456
  await aiInput('密码输入框', '123456');
  
  // 验证密码已输入（通常密码是隐藏的，所以验证输入框不为空）
  await aiAssert('密码输入框不为空');
  
  // 步骤3: 点击登录按钮
  await aiTap('登录按钮');
  
  // 等待登录处理完成
  await aiWaitFor('登录请求处理完成', { timeoutMs: 15000 });
  
  // 验证登录成功
  await aiAssert('登录成功，页面显示用户信息或跳转到主页');
  
  // 可选：获取登录后的用户信息进行验证
  const userInfo = await aiQuery<{username: string, loginTime: string}>(
    '获取当前登录用户的用户名和登录时间'
  );
  
  console.log("登录用户信息：", userInfo);
  expect(userInfo?.username).toBe('bijibo');
});

// 包含错误处理的版本
test("登录功能测试 - 错误处理版本", async ({
  ai,
  aiQuery,
  aiAssert,
  aiInput,
  aiTap,
  aiWaitFor
}) => {
  try {
    // 步骤1: 用户名输入框输入bijibo
    await aiInput('用户名输入框', 'bijibo');
    
    // 步骤2: 密码输入框输入123456
    await aiInput('密码输入框', '123456');
    
    // 步骤3: 点击登录
    await aiTap('登录按钮');
    
    // 等待登录结果
    await aiWaitFor('登录处理完成', { timeoutMs: 10000 });
    
    // 验证登录成功
    await aiAssert('登录成功，显示用户信息');
    
  } catch (error) {
    // 如果登录失败，验证错误信息
    await aiAssert('显示登录失败的错误信息');
    
    // 获取错误信息
    const errorMessage = await aiQuery<string>('获取登录失败的错误提示信息');
    console.log("登录失败原因：", errorMessage);
    
    throw error; // 重新抛出错误，让测试失败
  }
});

// 使用YAML配置的版本（如果你想要更结构化的测试）
test("登录功能测试 - YAML配置版本", async ({ page }) => {
  // 这个版本展示了如何使用midscene的YAML配置
  // 你可以在外部YAML文件中定义测试步骤
  
  const testConfig = {
    name: "登录测试",
    steps: [
      {
        action: "input",
        target: "用户名输入框",
        value: "bijibo"
      },
      {
        action: "input", 
        target: "密码输入框",
        value: "123456"
      },
      {
        action: "click",
        target: "登录按钮"
      },
      {
        action: "waitFor",
        target: "登录成功",
        timeout: 10000
      },
      {
        action: "assert",
        target: "用户已成功登录"
      }
    ]
  };
  
  // 这里需要midscene的YAML执行器
  // 具体实现取决于midscene的YAML支持
  console.log("使用YAML配置的测试：", testConfig);
}); 