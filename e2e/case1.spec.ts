import { expect } from "@playwright/test";
import { test } from "./fixture";

test.beforeEach(async ({ page }) => {
  await page.goto("https://www.baidu.com");
  await page.waitForLoadState("networkidle");
  console.log('OPENAI_API_KEY:', process.env.OPENAI_API_KEY);
});

test("在百度上点击搜索", async ({
  ai,
  aiQuery,
  aiAssert,
  aiInput,
  aiTap,
  aiScroll, aiWaitFor
}) => {
  //使用 aiInput 输入搜索关键词
//   await aiInput('搜索框', '笔记本电脑');

  // 使用 aiTap 点击搜索按钮
  await aiTap('百度一下按钮');

  // 等待搜索结果加载
//   await aiWaitFor('搜索结果列表已加载', { timeoutMs: 10000 });

 // 使用 aiScroll 滚动到页面底部
//   await aiScroll(
//     {
//       direction: 'down',
//       scrollType: 'once'
//     },
//     '搜索结果列表'
//   );

  // 使用 aiQuery 获取商品信息
//   const items = await aiQuery<Array<{title: string, price: number}>>(
//     '获取搜索结果中的商品标题和价格'
//   );

//   console.log("商品信息：", items);
//   expect(items?.length).toBeGreaterThan(0);

  // 使用 aiAssert 验证筛选功能
//   await aiAssert("界面左侧有类目筛选功能");
});

