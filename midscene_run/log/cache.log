[2025-08-06T21:27:15.579+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在ebay上搜索商品).cache.yaml
[2025-08-06T21:27:15.868+08:00] no unused cache found, type: locate, prompt: 百度一下按钮
[2025-08-06T21:47:20.740+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T21:47:21.012+08:00] no unused cache found, type: locate, prompt: 笔记本电脑
[2025-08-06T21:56:16.525+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T21:56:16.804+08:00] no unused cache found, type: locate, prompt: 笔记本电脑
[2025-08-06T22:01:26.469+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:01:26.792+08:00] no unused cache found, type: locate, prompt: 笔记本电脑
[2025-08-06T22:05:11.741+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:05:12.042+08:00] no unused cache found, type: locate, prompt: 笔记本电脑
[2025-08-06T22:05:38.327+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:05:38.613+08:00] no unused cache found, type: locate, prompt: 笔记本电脑
[2025-08-06T22:07:06.979+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:07:07.259+08:00] no unused cache found, type: locate, prompt: 百度一下按钮
[2025-08-06T22:08:09.241+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:08:09.780+08:00] no unused cache found, type: locate, prompt: 百度一下按钮
[2025-08-06T22:10:25.258+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:10:25.547+08:00] no unused cache found, type: locate, prompt: 百度一下按钮
[2025-08-06T22:10:43.250+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:10:43.546+08:00] no unused cache found, type: locate, prompt: 百度一下按钮
[2025-08-06T22:11:44.555+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:11:44.844+08:00] no unused cache found, type: locate, prompt: 百度一下按钮
[2025-08-06T22:13:32.949+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:13:34.045+08:00] no unused cache found, type: locate, prompt: 百度一下按钮
[2025-08-06T22:18:23.756+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:18:24.077+08:00] no unused cache found, type: locate, prompt: 百度一下按钮
[2025-08-06T22:22:15.693+08:00] no cache file found, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:22:16.032+08:00] no unused cache found, type: locate, prompt: 百度一下按钮
[2025-08-06T22:22:19.134+08:00] will append cache {
  type: 'locate',
  prompt: '百度一下按钮',
  xpaths: [
    '/html/body/div[1]/div[1]/div[6]/div[1]/div[1]/form[1]/span[2]/input[1]'
  ]
}
[2025-08-06T22:22:19.137+08:00] cache flushed to file: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:22:25.066+08:00] no unused cache found, type: locate, prompt: 搜索结果列表
[2025-08-06T22:25:30.199+08:00] cache loaded from file, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml, cache version: 0.25.1, record length: 1
[2025-08-06T22:25:30.467+08:00] cache found and marked as used, type: locate, prompt: 百度一下按钮, index: 0
[2025-08-06T22:25:33.599+08:00] will call updateFn to update cache, type: locate, prompt: 百度一下按钮, index: 0
[2025-08-06T22:25:33.599+08:00] cache updated, will flush to file, type: locate, prompt: 百度一下按钮, index: 0
[2025-08-06T22:25:33.601+08:00] cache flushed to file: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:27:15.540+08:00] cache loaded from file, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml, cache version: 0.25.1, record length: 1
[2025-08-06T22:27:15.820+08:00] cache found and marked as used, type: locate, prompt: 百度一下按钮, index: 0
[2025-08-06T22:27:18.964+08:00] will call updateFn to update cache, type: locate, prompt: 百度一下按钮, index: 0
[2025-08-06T22:27:18.964+08:00] cache updated, will flush to file, type: locate, prompt: 百度一下按钮, index: 0
[2025-08-06T22:27:18.966+08:00] cache flushed to file: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
[2025-08-06T22:29:59.407+08:00] cache loaded from file, path: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml, cache version: 0.25.1, record length: 1
[2025-08-06T22:29:59.694+08:00] cache found and marked as used, type: locate, prompt: 百度一下按钮, index: 0
[2025-08-06T22:30:02.800+08:00] will call updateFn to update cache, type: locate, prompt: 百度一下按钮, index: 0
[2025-08-06T22:30:02.800+08:00] cache updated, will flush to file, type: locate, prompt: 百度一下按钮, index: 0
[2025-08-06T22:30:02.801+08:00] cache flushed to file: E:\ai-test\midscene_run\cache\case1.spec.ts(在百度上点击搜索).cache.yaml
