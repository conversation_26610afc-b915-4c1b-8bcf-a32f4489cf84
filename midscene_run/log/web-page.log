[2025-08-06T21:27:15.724+08:00] waitForNavigation begin
[2025-08-06T21:27:15.724+08:00] waitForNavigation timeout: 5000
[2025-08-06T21:27:15.727+08:00] waitForNavigation begin
[2025-08-06T21:27:15.727+08:00] waitForNavigation timeout: 5000
[2025-08-06T21:27:15.764+08:00] waitForNavigation end
[2025-08-06T21:27:15.764+08:00] screenshotBase64 begin
[2025-08-06T21:27:15.771+08:00] waitForNavigation end
[2025-08-06T21:27:15.771+08:00] evaluate function begin
[2025-08-06T21:27:15.824+08:00] evaluate function end
[2025-08-06T21:27:15.824+08:00] getElementsNodeTree end, cost: 53ms
[2025-08-06T21:27:15.863+08:00] screenshotBase64 end, cost: 99ms
[2025-08-06T21:27:15.864+08:00] evaluate function begin
[2025-08-06T21:27:15.868+08:00] evaluate function end
[2025-08-06T21:47:20.866+08:00] waitForNavigation begin
[2025-08-06T21:47:20.866+08:00] waitForNavigation timeout: 5000
[2025-08-06T21:47:20.870+08:00] waitForNavigation begin
[2025-08-06T21:47:20.870+08:00] waitForNavigation timeout: 5000
[2025-08-06T21:47:20.910+08:00] waitForNavigation end
[2025-08-06T21:47:20.910+08:00] screenshotBase64 begin
[2025-08-06T21:47:20.913+08:00] waitForNavigation end
[2025-08-06T21:47:20.914+08:00] evaluate function begin
[2025-08-06T21:47:20.969+08:00] evaluate function end
[2025-08-06T21:47:20.969+08:00] getElementsNodeTree end, cost: 55ms
[2025-08-06T21:47:21.008+08:00] screenshotBase64 end, cost: 98ms
[2025-08-06T21:47:21.008+08:00] evaluate function begin
[2025-08-06T21:47:21.012+08:00] evaluate function end
[2025-08-06T21:56:16.658+08:00] waitForNavigation begin
[2025-08-06T21:56:16.658+08:00] waitForNavigation timeout: 5000
[2025-08-06T21:56:16.663+08:00] waitForNavigation begin
[2025-08-06T21:56:16.663+08:00] waitForNavigation timeout: 5000
[2025-08-06T21:56:16.701+08:00] waitForNavigation end
[2025-08-06T21:56:16.701+08:00] screenshotBase64 begin
[2025-08-06T21:56:16.704+08:00] waitForNavigation end
[2025-08-06T21:56:16.705+08:00] evaluate function begin
[2025-08-06T21:56:16.762+08:00] evaluate function end
[2025-08-06T21:56:16.762+08:00] getElementsNodeTree end, cost: 57ms
[2025-08-06T21:56:16.799+08:00] screenshotBase64 end, cost: 98ms
[2025-08-06T21:56:16.799+08:00] evaluate function begin
[2025-08-06T21:56:16.804+08:00] evaluate function end
[2025-08-06T22:01:26.631+08:00] waitForNavigation begin
[2025-08-06T22:01:26.631+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:01:26.634+08:00] waitForNavigation begin
[2025-08-06T22:01:26.634+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:01:26.673+08:00] waitForNavigation end
[2025-08-06T22:01:26.673+08:00] screenshotBase64 begin
[2025-08-06T22:01:26.677+08:00] waitForNavigation end
[2025-08-06T22:01:26.677+08:00] evaluate function begin
[2025-08-06T22:01:26.740+08:00] evaluate function end
[2025-08-06T22:01:26.740+08:00] getElementsNodeTree end, cost: 63ms
[2025-08-06T22:01:26.788+08:00] screenshotBase64 end, cost: 115ms
[2025-08-06T22:01:26.788+08:00] evaluate function begin
[2025-08-06T22:01:26.792+08:00] evaluate function end
[2025-08-06T22:05:11.879+08:00] waitForNavigation begin
[2025-08-06T22:05:11.880+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:05:11.883+08:00] waitForNavigation begin
[2025-08-06T22:05:11.883+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:05:11.929+08:00] waitForNavigation end
[2025-08-06T22:05:11.929+08:00] screenshotBase64 begin
[2025-08-06T22:05:11.933+08:00] waitForNavigation end
[2025-08-06T22:05:11.934+08:00] evaluate function begin
[2025-08-06T22:05:11.991+08:00] evaluate function end
[2025-08-06T22:05:11.991+08:00] getElementsNodeTree end, cost: 57ms
[2025-08-06T22:05:12.037+08:00] screenshotBase64 end, cost: 108ms
[2025-08-06T22:05:12.038+08:00] evaluate function begin
[2025-08-06T22:05:12.042+08:00] evaluate function end
[2025-08-06T22:05:38.455+08:00] waitForNavigation begin
[2025-08-06T22:05:38.455+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:05:38.459+08:00] waitForNavigation begin
[2025-08-06T22:05:38.459+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:05:38.500+08:00] waitForNavigation end
[2025-08-06T22:05:38.500+08:00] screenshotBase64 begin
[2025-08-06T22:05:38.504+08:00] waitForNavigation end
[2025-08-06T22:05:38.504+08:00] evaluate function begin
[2025-08-06T22:05:38.560+08:00] evaluate function end
[2025-08-06T22:05:38.561+08:00] getElementsNodeTree end, cost: 57ms
[2025-08-06T22:05:38.606+08:00] screenshotBase64 end, cost: 106ms
[2025-08-06T22:05:38.607+08:00] evaluate function begin
[2025-08-06T22:05:38.613+08:00] evaluate function end
[2025-08-06T22:07:07.107+08:00] waitForNavigation begin
[2025-08-06T22:07:07.108+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:07:07.111+08:00] waitForNavigation begin
[2025-08-06T22:07:07.111+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:07:07.147+08:00] waitForNavigation end
[2025-08-06T22:07:07.147+08:00] screenshotBase64 begin
[2025-08-06T22:07:07.151+08:00] waitForNavigation end
[2025-08-06T22:07:07.152+08:00] evaluate function begin
[2025-08-06T22:07:07.214+08:00] evaluate function end
[2025-08-06T22:07:07.214+08:00] getElementsNodeTree end, cost: 63ms
[2025-08-06T22:07:07.252+08:00] screenshotBase64 end, cost: 105ms
[2025-08-06T22:07:07.254+08:00] evaluate function begin
[2025-08-06T22:07:07.259+08:00] evaluate function end
[2025-08-06T22:08:09.512+08:00] waitForNavigation begin
[2025-08-06T22:08:09.512+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:08:09.516+08:00] waitForNavigation begin
[2025-08-06T22:08:09.516+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:08:09.614+08:00] waitForNavigation end
[2025-08-06T22:08:09.614+08:00] screenshotBase64 begin
[2025-08-06T22:08:09.625+08:00] waitForNavigation end
[2025-08-06T22:08:09.626+08:00] evaluate function begin
[2025-08-06T22:08:09.716+08:00] evaluate function end
[2025-08-06T22:08:09.716+08:00] getElementsNodeTree end, cost: 90ms
[2025-08-06T22:08:09.773+08:00] screenshotBase64 end, cost: 159ms
[2025-08-06T22:08:09.774+08:00] evaluate function begin
[2025-08-06T22:08:09.780+08:00] evaluate function end
[2025-08-06T22:10:25.386+08:00] waitForNavigation begin
[2025-08-06T22:10:25.386+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:10:25.390+08:00] waitForNavigation begin
[2025-08-06T22:10:25.390+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:10:25.432+08:00] waitForNavigation end
[2025-08-06T22:10:25.432+08:00] screenshotBase64 begin
[2025-08-06T22:10:25.436+08:00] waitForNavigation end
[2025-08-06T22:10:25.436+08:00] evaluate function begin
[2025-08-06T22:10:25.496+08:00] evaluate function end
[2025-08-06T22:10:25.496+08:00] getElementsNodeTree end, cost: 60ms
[2025-08-06T22:10:25.541+08:00] screenshotBase64 end, cost: 109ms
[2025-08-06T22:10:25.542+08:00] evaluate function begin
[2025-08-06T22:10:25.546+08:00] evaluate function end
[2025-08-06T22:10:43.381+08:00] waitForNavigation begin
[2025-08-06T22:10:43.381+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:10:43.385+08:00] waitForNavigation begin
[2025-08-06T22:10:43.385+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:10:43.429+08:00] waitForNavigation end
[2025-08-06T22:10:43.429+08:00] screenshotBase64 begin
[2025-08-06T22:10:43.433+08:00] waitForNavigation end
[2025-08-06T22:10:43.433+08:00] evaluate function begin
[2025-08-06T22:10:43.490+08:00] evaluate function end
[2025-08-06T22:10:43.490+08:00] getElementsNodeTree end, cost: 57ms
[2025-08-06T22:10:43.540+08:00] screenshotBase64 end, cost: 111ms
[2025-08-06T22:10:43.542+08:00] evaluate function begin
[2025-08-06T22:10:43.546+08:00] evaluate function end
[2025-08-06T22:11:44.695+08:00] waitForNavigation begin
[2025-08-06T22:11:44.695+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:11:44.698+08:00] waitForNavigation begin
[2025-08-06T22:11:44.699+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:11:44.737+08:00] waitForNavigation end
[2025-08-06T22:11:44.737+08:00] screenshotBase64 begin
[2025-08-06T22:11:44.742+08:00] waitForNavigation end
[2025-08-06T22:11:44.743+08:00] evaluate function begin
[2025-08-06T22:11:44.800+08:00] evaluate function end
[2025-08-06T22:11:44.800+08:00] getElementsNodeTree end, cost: 57ms
[2025-08-06T22:11:44.838+08:00] screenshotBase64 end, cost: 101ms
[2025-08-06T22:11:44.839+08:00] evaluate function begin
[2025-08-06T22:11:44.844+08:00] evaluate function end
[2025-08-06T22:13:33.499+08:00] waitForNavigation begin
[2025-08-06T22:13:33.499+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:13:33.514+08:00] waitForNavigation begin
[2025-08-06T22:13:33.514+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:13:33.665+08:00] waitForNavigation end
[2025-08-06T22:13:33.665+08:00] screenshotBase64 begin
[2025-08-06T22:13:33.698+08:00] waitForNavigation end
[2025-08-06T22:13:33.699+08:00] evaluate function begin
[2025-08-06T22:13:33.933+08:00] evaluate function end
[2025-08-06T22:13:33.940+08:00] getElementsNodeTree end, cost: 241ms
[2025-08-06T22:13:34.016+08:00] screenshotBase64 end, cost: 351ms
[2025-08-06T22:13:34.025+08:00] evaluate function begin
[2025-08-06T22:13:34.043+08:00] evaluate function end
[2025-08-06T22:18:23.909+08:00] waitForNavigation begin
[2025-08-06T22:18:23.909+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:18:23.914+08:00] waitForNavigation begin
[2025-08-06T22:18:23.914+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:18:23.954+08:00] waitForNavigation end
[2025-08-06T22:18:23.954+08:00] screenshotBase64 begin
[2025-08-06T22:18:23.962+08:00] waitForNavigation end
[2025-08-06T22:18:23.962+08:00] evaluate function begin
[2025-08-06T22:18:24.026+08:00] evaluate function end
[2025-08-06T22:18:24.026+08:00] getElementsNodeTree end, cost: 64ms
[2025-08-06T22:18:24.071+08:00] screenshotBase64 end, cost: 117ms
[2025-08-06T22:18:24.072+08:00] evaluate function begin
[2025-08-06T22:18:24.076+08:00] evaluate function end
[2025-08-06T22:22:15.832+08:00] waitForNavigation begin
[2025-08-06T22:22:15.833+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:15.836+08:00] waitForNavigation begin
[2025-08-06T22:22:15.836+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:15.898+08:00] waitForNavigation end
[2025-08-06T22:22:15.898+08:00] screenshotBase64 begin
[2025-08-06T22:22:15.903+08:00] waitForNavigation end
[2025-08-06T22:22:15.903+08:00] evaluate function begin
[2025-08-06T22:22:15.968+08:00] evaluate function end
[2025-08-06T22:22:15.968+08:00] getElementsNodeTree end, cost: 65ms
[2025-08-06T22:22:16.026+08:00] screenshotBase64 end, cost: 128ms
[2025-08-06T22:22:16.027+08:00] evaluate function begin
[2025-08-06T22:22:16.031+08:00] evaluate function end
[2025-08-06T22:22:19.122+08:00] evaluate function begin
[2025-08-06T22:22:19.134+08:00] evaluate function end
[2025-08-06T22:22:19.137+08:00] waitForNavigation begin
[2025-08-06T22:22:19.137+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:19.148+08:00] waitForNavigation end
[2025-08-06T22:22:19.148+08:00] screenshotBase64 begin
[2025-08-06T22:22:19.195+08:00] screenshotBase64 end, cost: 47ms
[2025-08-06T22:22:19.433+08:00] waitForNavigation begin
[2025-08-06T22:22:19.434+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:19.444+08:00] waitForNavigation end
[2025-08-06T22:22:19.444+08:00] screenshotBase64 begin
[2025-08-06T22:22:19.495+08:00] screenshotBase64 end, cost: 51ms
[2025-08-06T22:22:19.507+08:00] waitForNavigation begin
[2025-08-06T22:22:19.507+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:19.517+08:00] waitForNavigation end
[2025-08-06T22:22:19.517+08:00] screenshotBase64 begin
[2025-08-06T22:22:19.558+08:00] screenshotBase64 end, cost: 41ms
[2025-08-06T22:22:19.625+08:00] waitForNavigation begin
[2025-08-06T22:22:19.625+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:19.626+08:00] waitForNavigation begin
[2025-08-06T22:22:19.626+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:19.637+08:00] waitForNavigation end
[2025-08-06T22:22:19.637+08:00] screenshotBase64 begin
[2025-08-06T22:22:19.638+08:00] waitForNavigation end
[2025-08-06T22:22:19.638+08:00] evaluate function begin
[2025-08-06T22:22:19.660+08:00] evaluate function end
[2025-08-06T22:22:19.660+08:00] getElementsNodeTree end, cost: 22ms
[2025-08-06T22:22:19.708+08:00] screenshotBase64 end, cost: 71ms
[2025-08-06T22:22:19.781+08:00] waitForNavigation begin
[2025-08-06T22:22:19.781+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:19.782+08:00] waitForNavigation begin
[2025-08-06T22:22:19.782+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:20.079+08:00] waitForNavigation end
[2025-08-06T22:22:20.079+08:00] screenshotBase64 begin
[2025-08-06T22:22:20.081+08:00] waitForNavigation end
[2025-08-06T22:22:20.081+08:00] evaluate function begin
[2025-08-06T22:22:20.326+08:00] evaluate function end
[2025-08-06T22:22:20.326+08:00] getElementsNodeTree end, cost: 245ms
[2025-08-06T22:22:20.529+08:00] screenshotBase64 end, cost: 450ms
[2025-08-06T22:22:24.707+08:00] waitForNavigation begin
[2025-08-06T22:22:24.707+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:24.709+08:00] waitForNavigation begin
[2025-08-06T22:22:24.709+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:22:24.723+08:00] waitForNavigation end
[2025-08-06T22:22:24.723+08:00] screenshotBase64 begin
[2025-08-06T22:22:24.726+08:00] waitForNavigation end
[2025-08-06T22:22:24.727+08:00] evaluate function begin
[2025-08-06T22:22:25.015+08:00] evaluate function end
[2025-08-06T22:22:25.015+08:00] getElementsNodeTree end, cost: 288ms
[2025-08-06T22:22:25.066+08:00] screenshotBase64 end, cost: 343ms
[2025-08-06T22:25:30.323+08:00] waitForNavigation begin
[2025-08-06T22:25:30.323+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:25:30.326+08:00] waitForNavigation begin
[2025-08-06T22:25:30.326+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:25:30.363+08:00] waitForNavigation end
[2025-08-06T22:25:30.363+08:00] screenshotBase64 begin
[2025-08-06T22:25:30.367+08:00] waitForNavigation end
[2025-08-06T22:25:30.367+08:00] evaluate function begin
[2025-08-06T22:25:30.420+08:00] evaluate function end
[2025-08-06T22:25:30.420+08:00] getElementsNodeTree end, cost: 53ms
[2025-08-06T22:25:30.461+08:00] screenshotBase64 end, cost: 98ms
[2025-08-06T22:25:30.461+08:00] evaluate function begin
[2025-08-06T22:25:30.466+08:00] evaluate function end
[2025-08-06T22:25:33.591+08:00] evaluate function begin
[2025-08-06T22:25:33.599+08:00] evaluate function end
[2025-08-06T22:25:33.601+08:00] waitForNavigation begin
[2025-08-06T22:25:33.601+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:25:33.611+08:00] waitForNavigation end
[2025-08-06T22:25:33.611+08:00] screenshotBase64 begin
[2025-08-06T22:25:33.660+08:00] screenshotBase64 end, cost: 49ms
[2025-08-06T22:25:33.868+08:00] waitForNavigation begin
[2025-08-06T22:25:33.869+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:25:33.879+08:00] waitForNavigation end
[2025-08-06T22:25:33.879+08:00] screenshotBase64 begin
[2025-08-06T22:25:33.925+08:00] screenshotBase64 end, cost: 46ms
[2025-08-06T22:27:15.663+08:00] waitForNavigation begin
[2025-08-06T22:27:15.663+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:27:15.667+08:00] waitForNavigation begin
[2025-08-06T22:27:15.667+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:27:15.710+08:00] waitForNavigation end
[2025-08-06T22:27:15.710+08:00] screenshotBase64 begin
[2025-08-06T22:27:15.714+08:00] waitForNavigation end
[2025-08-06T22:27:15.715+08:00] evaluate function begin
[2025-08-06T22:27:15.768+08:00] evaluate function end
[2025-08-06T22:27:15.768+08:00] getElementsNodeTree end, cost: 53ms
[2025-08-06T22:27:15.816+08:00] screenshotBase64 end, cost: 106ms
[2025-08-06T22:27:15.816+08:00] evaluate function begin
[2025-08-06T22:27:15.820+08:00] evaluate function end
[2025-08-06T22:27:18.956+08:00] evaluate function begin
[2025-08-06T22:27:18.964+08:00] evaluate function end
[2025-08-06T22:27:18.966+08:00] waitForNavigation begin
[2025-08-06T22:27:18.966+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:27:18.975+08:00] waitForNavigation end
[2025-08-06T22:27:18.975+08:00] screenshotBase64 begin
[2025-08-06T22:27:19.016+08:00] screenshotBase64 end, cost: 41ms
[2025-08-06T22:27:19.246+08:00] waitForNavigation begin
[2025-08-06T22:27:19.246+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:27:19.258+08:00] waitForNavigation end
[2025-08-06T22:27:19.258+08:00] screenshotBase64 begin
[2025-08-06T22:27:19.297+08:00] screenshotBase64 end, cost: 39ms
[2025-08-06T22:29:59.531+08:00] waitForNavigation begin
[2025-08-06T22:29:59.531+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:29:59.537+08:00] waitForNavigation begin
[2025-08-06T22:29:59.537+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:29:59.582+08:00] waitForNavigation end
[2025-08-06T22:29:59.583+08:00] screenshotBase64 begin
[2025-08-06T22:29:59.588+08:00] waitForNavigation end
[2025-08-06T22:29:59.589+08:00] evaluate function begin
[2025-08-06T22:29:59.639+08:00] evaluate function end
[2025-08-06T22:29:59.639+08:00] getElementsNodeTree end, cost: 50ms
[2025-08-06T22:29:59.689+08:00] screenshotBase64 end, cost: 106ms
[2025-08-06T22:29:59.690+08:00] evaluate function begin
[2025-08-06T22:29:59.693+08:00] evaluate function end
[2025-08-06T22:30:02.791+08:00] evaluate function begin
[2025-08-06T22:30:02.799+08:00] evaluate function end
[2025-08-06T22:30:02.802+08:00] waitForNavigation begin
[2025-08-06T22:30:02.802+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:30:02.811+08:00] waitForNavigation end
[2025-08-06T22:30:02.811+08:00] screenshotBase64 begin
[2025-08-06T22:30:02.854+08:00] screenshotBase64 end, cost: 43ms
[2025-08-06T22:30:03.080+08:00] waitForNavigation begin
[2025-08-06T22:30:03.080+08:00] waitForNavigation timeout: 5000
[2025-08-06T22:30:03.094+08:00] waitForNavigation end
[2025-08-06T22:30:03.095+08:00] screenshotBase64 begin
[2025-08-06T22:30:03.137+08:00] screenshotBase64 end, cost: 42ms
