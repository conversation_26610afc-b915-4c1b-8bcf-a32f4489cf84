[2025-08-06T21:27:15.624+08:00] Getting page URL
[2025-08-06T21:27:15.624+08:00] URL end
[2025-08-06T21:27:15.624+08:00] Uploading test info to server
[2025-08-06T21:27:15.724+08:00] UploadTestInfoToServer end
[2025-08-06T21:27:15.724+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T21:27:15.824+08:00] GetElementsNodeTree end
[2025-08-06T21:27:15.863+08:00] ScreenshotBase64 end
[2025-08-06T21:27:15.863+08:00] ParseContextFromWebPage end
[2025-08-06T21:27:15.863+08:00] Traversing element tree
[2025-08-06T21:27:15.864+08:00] TraverseTree end
[2025-08-06T21:27:15.868+08:00] size: 1280x720 dpr: 1
[2025-08-06T21:47:20.785+08:00] Getting page URL
[2025-08-06T21:47:20.786+08:00] URL end
[2025-08-06T21:47:20.786+08:00] Uploading test info to server
[2025-08-06T21:47:20.866+08:00] UploadTestInfoToServer end
[2025-08-06T21:47:20.866+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T21:47:20.969+08:00] GetElementsNodeTree end
[2025-08-06T21:47:21.008+08:00] ScreenshotBase64 end
[2025-08-06T21:47:21.008+08:00] ParseContextFromWebPage end
[2025-08-06T21:47:21.008+08:00] Traversing element tree
[2025-08-06T21:47:21.008+08:00] TraverseTree end
[2025-08-06T21:47:21.012+08:00] size: 1280x720 dpr: 1
[2025-08-06T21:56:16.571+08:00] Getting page URL
[2025-08-06T21:56:16.571+08:00] URL end
[2025-08-06T21:56:16.571+08:00] Uploading test info to server
[2025-08-06T21:56:16.658+08:00] UploadTestInfoToServer end
[2025-08-06T21:56:16.658+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T21:56:16.762+08:00] GetElementsNodeTree end
[2025-08-06T21:56:16.799+08:00] ScreenshotBase64 end
[2025-08-06T21:56:16.799+08:00] ParseContextFromWebPage end
[2025-08-06T21:56:16.799+08:00] Traversing element tree
[2025-08-06T21:56:16.799+08:00] TraverseTree end
[2025-08-06T21:56:16.804+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:01:26.517+08:00] Getting page URL
[2025-08-06T22:01:26.517+08:00] URL end
[2025-08-06T22:01:26.517+08:00] Uploading test info to server
[2025-08-06T22:01:26.631+08:00] UploadTestInfoToServer end
[2025-08-06T22:01:26.631+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:01:26.740+08:00] GetElementsNodeTree end
[2025-08-06T22:01:26.788+08:00] ScreenshotBase64 end
[2025-08-06T22:01:26.788+08:00] ParseContextFromWebPage end
[2025-08-06T22:01:26.788+08:00] Traversing element tree
[2025-08-06T22:01:26.788+08:00] TraverseTree end
[2025-08-06T22:01:26.792+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:05:11.788+08:00] Getting page URL
[2025-08-06T22:05:11.788+08:00] URL end
[2025-08-06T22:05:11.788+08:00] Uploading test info to server
[2025-08-06T22:05:11.879+08:00] UploadTestInfoToServer end
[2025-08-06T22:05:11.879+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:05:11.991+08:00] GetElementsNodeTree end
[2025-08-06T22:05:12.037+08:00] ScreenshotBase64 end
[2025-08-06T22:05:12.037+08:00] ParseContextFromWebPage end
[2025-08-06T22:05:12.037+08:00] Traversing element tree
[2025-08-06T22:05:12.038+08:00] TraverseTree end
[2025-08-06T22:05:12.042+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:05:38.374+08:00] Getting page URL
[2025-08-06T22:05:38.374+08:00] URL end
[2025-08-06T22:05:38.374+08:00] Uploading test info to server
[2025-08-06T22:05:38.455+08:00] UploadTestInfoToServer end
[2025-08-06T22:05:38.455+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:05:38.561+08:00] GetElementsNodeTree end
[2025-08-06T22:05:38.606+08:00] ScreenshotBase64 end
[2025-08-06T22:05:38.607+08:00] ParseContextFromWebPage end
[2025-08-06T22:05:38.607+08:00] Traversing element tree
[2025-08-06T22:05:38.607+08:00] TraverseTree end
[2025-08-06T22:05:38.613+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:07:07.024+08:00] Getting page URL
[2025-08-06T22:07:07.024+08:00] URL end
[2025-08-06T22:07:07.024+08:00] Uploading test info to server
[2025-08-06T22:07:07.107+08:00] UploadTestInfoToServer end
[2025-08-06T22:07:07.107+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:07:07.214+08:00] GetElementsNodeTree end
[2025-08-06T22:07:07.252+08:00] ScreenshotBase64 end
[2025-08-06T22:07:07.253+08:00] ParseContextFromWebPage end
[2025-08-06T22:07:07.253+08:00] Traversing element tree
[2025-08-06T22:07:07.253+08:00] TraverseTree end
[2025-08-06T22:07:07.259+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:08:09.330+08:00] Getting page URL
[2025-08-06T22:08:09.330+08:00] URL end
[2025-08-06T22:08:09.330+08:00] Uploading test info to server
[2025-08-06T22:08:09.512+08:00] UploadTestInfoToServer end
[2025-08-06T22:08:09.512+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:08:09.716+08:00] GetElementsNodeTree end
[2025-08-06T22:08:09.773+08:00] ScreenshotBase64 end
[2025-08-06T22:08:09.773+08:00] ParseContextFromWebPage end
[2025-08-06T22:08:09.773+08:00] Traversing element tree
[2025-08-06T22:08:09.774+08:00] TraverseTree end
[2025-08-06T22:08:09.780+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:10:25.303+08:00] Getting page URL
[2025-08-06T22:10:25.303+08:00] URL end
[2025-08-06T22:10:25.303+08:00] Uploading test info to server
[2025-08-06T22:10:25.385+08:00] UploadTestInfoToServer end
[2025-08-06T22:10:25.385+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:10:25.496+08:00] GetElementsNodeTree end
[2025-08-06T22:10:25.541+08:00] ScreenshotBase64 end
[2025-08-06T22:10:25.541+08:00] ParseContextFromWebPage end
[2025-08-06T22:10:25.541+08:00] Traversing element tree
[2025-08-06T22:10:25.542+08:00] TraverseTree end
[2025-08-06T22:10:25.546+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:10:43.297+08:00] Getting page URL
[2025-08-06T22:10:43.297+08:00] URL end
[2025-08-06T22:10:43.297+08:00] Uploading test info to server
[2025-08-06T22:10:43.381+08:00] UploadTestInfoToServer end
[2025-08-06T22:10:43.381+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:10:43.491+08:00] GetElementsNodeTree end
[2025-08-06T22:10:43.541+08:00] ScreenshotBase64 end
[2025-08-06T22:10:43.541+08:00] ParseContextFromWebPage end
[2025-08-06T22:10:43.541+08:00] Traversing element tree
[2025-08-06T22:10:43.542+08:00] TraverseTree end
[2025-08-06T22:10:43.546+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:11:44.604+08:00] Getting page URL
[2025-08-06T22:11:44.604+08:00] URL end
[2025-08-06T22:11:44.604+08:00] Uploading test info to server
[2025-08-06T22:11:44.695+08:00] UploadTestInfoToServer end
[2025-08-06T22:11:44.695+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:11:44.800+08:00] GetElementsNodeTree end
[2025-08-06T22:11:44.838+08:00] ScreenshotBase64 end
[2025-08-06T22:11:44.838+08:00] ParseContextFromWebPage end
[2025-08-06T22:11:44.838+08:00] Traversing element tree
[2025-08-06T22:11:44.839+08:00] TraverseTree end
[2025-08-06T22:11:44.844+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:13:33.134+08:00] Getting page URL
[2025-08-06T22:13:33.140+08:00] URL end
[2025-08-06T22:13:33.142+08:00] Uploading test info to server
[2025-08-06T22:13:33.498+08:00] UploadTestInfoToServer end
[2025-08-06T22:13:33.498+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:13:33.941+08:00] GetElementsNodeTree end
[2025-08-06T22:13:34.018+08:00] ScreenshotBase64 end
[2025-08-06T22:13:34.020+08:00] ParseContextFromWebPage end
[2025-08-06T22:13:34.020+08:00] Traversing element tree
[2025-08-06T22:13:34.024+08:00] TraverseTree end
[2025-08-06T22:13:34.044+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:18:23.809+08:00] Getting page URL
[2025-08-06T22:18:23.809+08:00] URL end
[2025-08-06T22:18:23.809+08:00] Uploading test info to server
[2025-08-06T22:18:23.908+08:00] UploadTestInfoToServer end
[2025-08-06T22:18:23.908+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:18:24.026+08:00] GetElementsNodeTree end
[2025-08-06T22:18:24.071+08:00] ScreenshotBase64 end
[2025-08-06T22:18:24.071+08:00] ParseContextFromWebPage end
[2025-08-06T22:18:24.071+08:00] Traversing element tree
[2025-08-06T22:18:24.072+08:00] TraverseTree end
[2025-08-06T22:18:24.076+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:22:15.741+08:00] Getting page URL
[2025-08-06T22:22:15.741+08:00] URL end
[2025-08-06T22:22:15.741+08:00] Uploading test info to server
[2025-08-06T22:22:15.832+08:00] UploadTestInfoToServer end
[2025-08-06T22:22:15.832+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:22:15.968+08:00] GetElementsNodeTree end
[2025-08-06T22:22:16.026+08:00] ScreenshotBase64 end
[2025-08-06T22:22:16.026+08:00] ParseContextFromWebPage end
[2025-08-06T22:22:16.026+08:00] Traversing element tree
[2025-08-06T22:22:16.027+08:00] TraverseTree end
[2025-08-06T22:22:16.031+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:22:19.558+08:00] Getting page URL
[2025-08-06T22:22:19.558+08:00] URL end
[2025-08-06T22:22:19.558+08:00] Uploading test info to server
[2025-08-06T22:22:19.625+08:00] UploadTestInfoToServer end
[2025-08-06T22:22:19.625+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:22:19.661+08:00] GetElementsNodeTree end
[2025-08-06T22:22:19.708+08:00] ScreenshotBase64 end
[2025-08-06T22:22:19.708+08:00] ParseContextFromWebPage end
[2025-08-06T22:22:19.708+08:00] Traversing element tree
[2025-08-06T22:22:19.708+08:00] TraverseTree end
[2025-08-06T22:22:19.708+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:22:19.709+08:00] Getting page URL
[2025-08-06T22:22:19.709+08:00] URL end
[2025-08-06T22:22:19.709+08:00] Uploading test info to server
[2025-08-06T22:22:19.781+08:00] UploadTestInfoToServer end
[2025-08-06T22:22:19.781+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:22:20.326+08:00] GetElementsNodeTree end
[2025-08-06T22:22:20.529+08:00] ScreenshotBase64 end
[2025-08-06T22:22:20.529+08:00] ParseContextFromWebPage end
[2025-08-06T22:22:20.529+08:00] Traversing element tree
[2025-08-06T22:22:20.529+08:00] TraverseTree end
[2025-08-06T22:22:20.529+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:22:24.615+08:00] Getting page URL
[2025-08-06T22:22:24.615+08:00] URL end
[2025-08-06T22:22:24.615+08:00] Uploading test info to server
[2025-08-06T22:22:24.707+08:00] UploadTestInfoToServer end
[2025-08-06T22:22:24.707+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:22:25.015+08:00] GetElementsNodeTree end
[2025-08-06T22:22:25.066+08:00] ScreenshotBase64 end
[2025-08-06T22:22:25.066+08:00] ParseContextFromWebPage end
[2025-08-06T22:22:25.066+08:00] Traversing element tree
[2025-08-06T22:22:25.066+08:00] TraverseTree end
[2025-08-06T22:22:25.066+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:25:30.247+08:00] Getting page URL
[2025-08-06T22:25:30.248+08:00] URL end
[2025-08-06T22:25:30.248+08:00] Uploading test info to server
[2025-08-06T22:25:30.323+08:00] UploadTestInfoToServer end
[2025-08-06T22:25:30.323+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:25:30.420+08:00] GetElementsNodeTree end
[2025-08-06T22:25:30.461+08:00] ScreenshotBase64 end
[2025-08-06T22:25:30.461+08:00] ParseContextFromWebPage end
[2025-08-06T22:25:30.461+08:00] Traversing element tree
[2025-08-06T22:25:30.461+08:00] TraverseTree end
[2025-08-06T22:25:30.466+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:27:15.587+08:00] Getting page URL
[2025-08-06T22:27:15.587+08:00] URL end
[2025-08-06T22:27:15.587+08:00] Uploading test info to server
[2025-08-06T22:27:15.663+08:00] UploadTestInfoToServer end
[2025-08-06T22:27:15.663+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:27:15.768+08:00] GetElementsNodeTree end
[2025-08-06T22:27:15.816+08:00] ScreenshotBase64 end
[2025-08-06T22:27:15.816+08:00] ParseContextFromWebPage end
[2025-08-06T22:27:15.816+08:00] Traversing element tree
[2025-08-06T22:27:15.816+08:00] TraverseTree end
[2025-08-06T22:27:15.820+08:00] size: 1280x720 dpr: 1
[2025-08-06T22:29:59.452+08:00] Getting page URL
[2025-08-06T22:29:59.452+08:00] URL end
[2025-08-06T22:29:59.452+08:00] Uploading test info to server
[2025-08-06T22:29:59.530+08:00] UploadTestInfoToServer end
[2025-08-06T22:29:59.530+08:00] Starting parallel operations: screenshot and element tree
[2025-08-06T22:29:59.639+08:00] GetElementsNodeTree end
[2025-08-06T22:29:59.689+08:00] ScreenshotBase64 end
[2025-08-06T22:29:59.689+08:00] ParseContextFromWebPage end
[2025-08-06T22:29:59.689+08:00] Traversing element tree
[2025-08-06T22:29:59.690+08:00] TraverseTree end
[2025-08-06T22:29:59.693+08:00] size: 1280x720 dpr: 1
